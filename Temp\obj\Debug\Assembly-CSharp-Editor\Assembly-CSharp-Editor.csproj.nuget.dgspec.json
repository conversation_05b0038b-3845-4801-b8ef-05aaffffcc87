{"format": 1, "restore": {"D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-Editor.csproj": {}}, "projects": {"D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-Editor-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-Editor-firstpass.csproj", "projectName": "Assembly-<PERSON><PERSON><PERSON>-Editor-first<PERSON>", "projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-Editor-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\obj\\Debug\\Assembly-CSharp-Editor-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-firstpass.csproj"}, "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj": {"projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\obj\\Debug\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-Editor-firstpass.csproj": {"projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-Editor-firstpass.csproj"}, "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-firstpass.csproj"}, "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp.csproj": {"projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp.csproj"}, "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj": {"projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\obj\\Debug\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj": {"projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Assembly-CSharp-firstpass.csproj"}, "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj": {"projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj", "projectName": "GoogleMobileAds.Editor", "projectPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\GoogleMobileAds.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Temp\\obj\\Debug\\GoogleMobileAds.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}