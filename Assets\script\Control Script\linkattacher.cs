using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class linkattacher : MonoBehaviour
{
    public GameObject grasscutter, groundflater, grasspick, saeedmachine, plough;
     public GameObject grasscutter1, groundflater1, grasspick1, saeedmachine1, plough1;

    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.CompareTag("Grasscutter"))
        {
            grasscutter.SetActive(true);
            grasscutter1.SetActive(false);

        }
        else if (other.gameObject.CompareTag("Groundflater"))
        {
            groundflater.SetActive(true);
            groundflater1.SetActive(false);
        }
        else if (other.gameObject.CompareTag("Grasspicker"))
        {
            grasspick.SetActive(true);
            grasspick1.SetActive(false);
        }
        else if (other.gameObject.CompareTag("Saeedmachine"))
        {
            saeedmachine.SetActive(true);
            saeedmachine1.SetActive(false);
        }
        else if (other.gameObject.CompareTag("Plough"))
        {
            plough.SetActive(true);
            plough1.SetActive(false);
        }



    }
}
