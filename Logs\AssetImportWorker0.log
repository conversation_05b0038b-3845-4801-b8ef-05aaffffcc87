Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker0.log
-srvPort
54481
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 124.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56104
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.011970 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 399 ms
Refreshing native plugins compatible for Editor in 121.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.493 seconds
Domain Reload Profiling:
	ReloadAssembly (1494ms)
		BeginReloadAssembly (154ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1180ms)
			LoadAssemblies (151ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (192ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (874ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (518ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (122ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (163ms)
				ProcessInitializeOnLoadMethodAttributes (69ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.014839 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 116.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.698 seconds
Domain Reload Profiling:
	ReloadAssembly (2699ms)
		BeginReloadAssembly (233ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (2287ms)
			LoadAssemblies (176ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (397ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (125ms)
			SetupLoadedEditorAssemblies (1574ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (21ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (117ms)
				BeforeProcessingInitializeOnLoad (110ms)
				ProcessInitializeOnLoadAttributes (1218ms)
				ProcessInitializeOnLoadMethodAttributes (96ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3722 Unused Serialized files (Serialized files now loaded: 0)
Unloading 177 unused Assets / (0.7 MB). Loaded Objects now: 4144.
Memory consumption went from 175.6 MB to 174.9 MB.
Total: 7.942800 ms (FindLiveObjects: 0.522600 ms CreateObjectMapping: 0.254100 ms MarkObjects: 6.156500 ms  DeleteObjects: 1.008700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 5519.431684 seconds.
  path: Assets/script/Ui/sample + slicing/slicing/menu/setting icon.png
  artifactKey: Guid(4633c6316f1aa854287cf5e0c780d5b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/script/Ui/sample + slicing/slicing/menu/setting icon.png using Guid(4633c6316f1aa854287cf5e0c780d5b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '89305cc844f61e7356c4cd9c01808922') in 0.208058 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/script/Ui/sample + slicing/slicing/menu/setting box.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/script/Ui/sample + slicing/slicing/menu/setting box.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ff036e12f60f86505096dad9dae19e44') in 0.039777 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 15.042000 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Mobile Textures/steering wheel.png
  artifactKey: Guid(5e11606abcefdca40b370201cfce86af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Mobile Textures/steering wheel.png using Guid(5e11606abcefdca40b370201cfce86af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7e986b6510371f9673c674b74399984f') in 0.045039 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 1.234174 seconds.
  path: Assets/script/Ui/sample + slicing/slicing/modes/tochan.png
  artifactKey: Guid(b33645e0d5ab85d40ada40794cd40aba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/script/Ui/sample + slicing/slicing/modes/tochan.png using Guid(b33645e0d5ab85d40ada40794cd40aba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '41819ff79690de7fd19e10a21afb368d') in 0.065678 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/script/Ui/sample + slicing/slicing/failed/total reward .png
  artifactKey: Guid(d29562c1c60ab1f4a9e9f1fef22f2931) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/script/Ui/sample + slicing/slicing/failed/total reward .png using Guid(d29562c1c60ab1f4a9e9f1fef22f2931) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f9afa14f1250af18dcdf3dbbf04c52f9') in 0.028342 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.238503 seconds.
  path: Assets/script/Ui/sample + slicing/slicing/failed/YOU LOSE.png
  artifactKey: Guid(97d75d64d9c0870419439c368b1d6956) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/script/Ui/sample + slicing/slicing/failed/YOU LOSE.png using Guid(97d75d64d9c0870419439c368b1d6956) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '07b1d8d5dff605242d1249bfea0501dd') in 0.033095 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 4.179506 seconds.
  path: Assets/script/Ui/sample + slicing/slicing/pause/resume icon.png
  artifactKey: Guid(38fd510ccfac8da4db1ae57570ac8a55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/script/Ui/sample + slicing/slicing/pause/resume icon.png using Guid(38fd510ccfac8da4db1ae57570ac8a55) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e25e51319bebd7be79873f50048db081') in 0.037588 seconds 
Number of asset objects unloaded after import = 3
